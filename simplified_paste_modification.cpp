// 简化版本：只显示需要修改的粘贴部分核心代码
// 主要修改在粘贴功能的位置计算部分

else if (keyEvent->key() == Qt::Key_V)
{
    if (m_pPageManager->getCurSelectPage() != m_root_host) return false;

    XMLObject xmlObj;
    QString copyDat = QApplication::clipboard()->text();

    if (!xmlObj.load(copyDat, nullptr))
    {
        return false;
    }

    bool hasValidHosts = false;
    QUndoCommand* cmd = new QUndoCommand;

    // 获取目标父类
    CMvAbstractHost* targetParent = m_currentPasteParent ? m_currentPasteParent : m_root_host;

    // ========== 核心修改：获取鼠标位置 ==========
    // 获取鼠标当前位置（全局坐标）
    QPoint globalMousePos = QCursor::pos();
    
    // 将全局坐标转换为目标父控件的本地坐标
    QWidget* targetWidget = nullptr;
    
    // 方法1：如果您的CMvAbstractHost有直接获取QWidget的方法
    // targetWidget = targetParent->getWidget();
    
    // 方法2：如果需要通过其他方式获取
    // targetWidget = m_widget_to_host.key(targetParent);
    
    // 方法3：如果有特定的获取方法
    // targetWidget = targetParent->getAssociatedWidget();
    
    // 临时方案：假设您有某种方式获取widget
    // 请根据您的实际代码结构调整这部分
    foreach(QWidget* widget, m_widget_to_host.keys())
    {
        if (m_widget_to_host.value(widget) == targetParent)
        {
            targetWidget = widget;
            break;
        }
    }
    
    QPoint localMousePos;
    if (targetWidget)
    {
        localMousePos = targetWidget->mapFromGlobal(globalMousePos);
    }
    else
    {
        // 如果无法获取目标widget，使用相对于根控件的坐标
        localMousePos = globalMousePos;
    }

    // 获取复制的控件列表
    QList<int> index;
    QList<CMvAbstractHost*> list;
    QList<XMLObject*> xmlHosts = xmlObj.getChildren();
    
    // ========== 核心修改：计算基于鼠标位置的偏移 ==========
    // 计算所有控件的边界框
    QRect boundingRect;
    bool firstRect = true;
    
    for (int i = 0; i < xmlHosts.size() && i < m_originalCenters.size(); ++i)
    {
        if (i < m_originalCenters.size())
        {
            QPoint center = m_originalCenters[i];
            QRect rect(center.x() - 50, center.y() - 25, 100, 50); // 假设默认大小
            
            if (firstRect)
            {
                boundingRect = rect;
                firstRect = false;
            }
            else
            {
                boundingRect = boundingRect.united(rect);
            }
        }
    }
    
    // 计算偏移量：鼠标位置 - 边界框中心
    QPoint mouseOffset = localMousePos - boundingRect.center();
    
    // 如果不是第一次粘贴，添加额外偏移避免重叠
    if (!m_isFirstPasteAfterCopy)
    {
        mouseOffset += QPoint(20, 20);
    }

    // ========== 修改：应用新的位置计算 ==========
    for (int i = 0; i < xmlHosts.size(); ++i)
    {
        if (i >= m_originalCenters.size()) break;

        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* pHostObj = CMvHostFactory::createHost(xmlHost);
        if (pHostObj != nullptr)
        {
            pHostObj->generateLayoutInAdvance(xmlHost);
            pHostObj->fromObject(xmlHost);
            pHostObj->setDefault();
            pHostObj->setId(targetParent, m_pPageManager);
            
            // 获取原始几何信息
            QRect originalGeometry = pHostObj->getPropertyValue("geometry").toRect();
            
            // 计算新位置：原始位置 + 鼠标偏移
            QRect newGeometry = originalGeometry;
            newGeometry.translate(mouseOffset);
            
            // 可选：边界检查，确保不超出父控件范围
            if (targetWidget)
            {
                QRect parentRect = targetWidget->rect();
                
                // 调整位置确保在父控件内
                if (newGeometry.right() > parentRect.right())
                    newGeometry.moveRight(parentRect.right());
                if (newGeometry.bottom() > parentRect.bottom())
                    newGeometry.moveBottom(parentRect.bottom());
                if (newGeometry.left() < parentRect.left())
                    newGeometry.moveLeft(parentRect.left());
                if (newGeometry.top() < parentRect.top())
                    newGeometry.moveTop(parentRect.top());
            }
            
            // 设置新的几何位置
            pHostObj->setPropertyValue("geometry", newGeometry);

            // 更新最后粘贴位置记录
            if (i < m_lastPastePositions.size())
            {
                m_lastPastePositions[i] = newGeometry.center();
            }
            else
            {
                m_lastPastePositions.append(newGeometry.center());
            }

            list.append(pHostObj);
            index.append(targetParent->getChildCount() + i);
            hasValidHosts = true;
        }
    }

    // 第一次粘贴完成后标记
    if (hasValidHosts && m_isFirstPasteAfterCopy)
    {
        m_isFirstPasteAfterCopy = false;
    }

    // 其余代码保持不变...
    if (hasValidHosts)
    {
        new CMvAddHostUndoCommand(m_pPageManager, targetParent, list, index, AHT_ADD, cmd);
        m_pUndoStack->push(cmd);

        QList<CMvAbstractHost*> allHost;
        for (auto host : list)
        {
            host->getAllDescendants(allHost);
        }

        for (auto host : allHost)
        {
            QString dataBind = host->getPropertyValue("dataBind").toString();
            if (!dataBind.isEmpty())
            {
                QVariant var(dataBind);
                host->generateEventCode("dataBind", var, m_pPageManager, false);
            }

            QString suffix = "";
            QStringList eventList;
            host->getEventPropertyName(suffix, eventList);
            for (auto str : eventList)
            {
                QString element = host->getPropertyValue(str).toString();
                if (!element.isEmpty())
                {
                    QVariant var(element);
                    host->generateEventCode(str, var, m_pPageManager, false);
                }
            }
        }
    }
}

/*
主要修改说明：

1. 使用 QCursor::pos() 获取鼠标全局位置
2. 使用 mapFromGlobal() 将全局坐标转换为父控件本地坐标
3. 计算所有选中控件的边界框
4. 计算偏移量：鼠标位置 - 边界框中心
5. 对每个控件应用这个偏移量
6. 可选的边界检查，确保控件不超出父控件范围

需要根据您的实际代码调整的部分：
- 获取CMvAbstractHost对应QWidget的方法
- 可能需要调整坐标系统的处理方式
*/
