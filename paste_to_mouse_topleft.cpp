// 修改后的粘贴代码 - 控件左上角定位到鼠标位置
else if (keyEvent->key() == Qt::Key_V)
{
    if (m_pPageManager->getCurSelectPage() != m_root_host) return false;

    XMLObject xmlObj;
    QString copyDat = QApplication::clipboard()->text();

    if (!xmlObj.load(copyDat, nullptr))
    {
        return false;
    }

    bool hasValidHosts = false;
    QUndoCommand* cmd = new QUndoCommand;

    // 获取目标父类
    CMvAbstractHost* targetParent = m_currentPasteParent ? m_currentPasteParent : m_root_host;

    // 获取鼠标位置 ==========
    // 获取鼠标当前位置（全局坐标）
    QPoint globalMousePos = QCursor::pos();

    // 将全局坐标转换为目标父控件的本地坐标
    QWidget* targetWidget = nullptr;

    // 获取目标控件
    targetWidget = (QWidget *)targetParent->getObject();

    QPoint localMousePos;
    if (targetWidget)
    {
        localMousePos = targetWidget->mapFromGlobal(globalMousePos);
    }
    else
    {
        // 如果无法获取目标widget，使用相对于根控件的坐标
        localMousePos = globalMousePos;
    }

    // 获取复制的控件列表
    QList<int> index;
    QList<CMvAbstractHost*> list;
    QList<XMLObject*> xmlHosts = xmlObj.getChildren();

    // 计算基于鼠标位置的偏移 ==========
    // 找到所有控件的最左上角位置
    QPoint topLeftMost;
    bool firstPoint = true;

    // 先遍历一次，找到控件组的最左上角
    for (int i = 0; i < xmlHosts.size(); ++i)
    {
        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* tempHost = CMvHostFactory::createHost(xmlHost);
        if (tempHost != nullptr)
        {
            tempHost->generateLayoutInAdvance(xmlHost);
            tempHost->fromObject(xmlHost);
            
            QRect geometry = tempHost->getPropertyValue("geometry").toRect();
            QPoint topLeft = geometry.topLeft();
            
            if (firstPoint)
            {
                topLeftMost = topLeft;
                firstPoint = false;
            }
            else
            {
                // 找到最左上角的点
                if (topLeft.x() < topLeftMost.x())
                    topLeftMost.setX(topLeft.x());
                if (topLeft.y() < topLeftMost.y())
                    topLeftMost.setY(topLeft.y());
            }
            
            delete tempHost; // 临时对象，用完即删
        }
    }

    // 计算偏移量：鼠标位置 - 控件组的最左上角
    QPoint mouseOffset = localMousePos - topLeftMost;

    // 如果不是第一次粘贴，添加额外偏移避免重叠
    if (!m_isFirstPasteAfterCopy)
    {
        mouseOffset += QPoint(20, 20);
    }

    // 应用新的位置计算 ==========
    for (int i = 0; i < xmlHosts.size(); ++i)
    {
        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* pHostObj = CMvHostFactory::createHost(xmlHost);
        if (pHostObj != nullptr)
        {
            pHostObj->generateLayoutInAdvance(xmlHost);
            pHostObj->fromObject(xmlHost);
            pHostObj->setDefault();
            pHostObj->setId(targetParent, m_pPageManager);

            // 获取原始几何信息
            QRect originalGeometry = pHostObj->getPropertyValue("geometry").toRect();

            // 计算新位置：原始位置 + 鼠标偏移
            QRect newGeometry = originalGeometry;
            newGeometry.translate(mouseOffset);

            // 边界检查，确保不超出父控件范围
            if (targetWidget)
            {
                QRect parentRect = targetWidget->rect();

                // 调整位置确保在父控件内
                if (newGeometry.right() > parentRect.right())
                    newGeometry.moveRight(parentRect.right());
                if (newGeometry.bottom() > parentRect.bottom())
                    newGeometry.moveBottom(parentRect.bottom());
                if (newGeometry.left() < parentRect.left())
                    newGeometry.moveLeft(parentRect.left());
                if (newGeometry.top() < parentRect.top())
                    newGeometry.moveTop(parentRect.top());
            }

            // 设置新的几何位置
            pHostObj->setPropertyValue("geometry", newGeometry);

            // 更新最后粘贴位置记录
            if (i < m_lastPastePositions.size())
            {
                m_lastPastePositions[i] = newGeometry.center();
            }
            else
            {
                m_lastPastePositions.append(newGeometry.center());
            }

            list.append(pHostObj);
            index.append(targetParent->getChildCount() + i);
            hasValidHosts = true;
        }
    }

    // 第一次粘贴完成后标记
    if (hasValidHosts && m_isFirstPasteAfterCopy)
    {
        m_isFirstPasteAfterCopy = false;
    }

    if (hasValidHosts)
    {
        new CMvAddHostUndoCommand(m_pPageManager, targetParent, list, index, AHT_ADD, cmd);
        m_pUndoStack->push(cmd);

        QList<CMvAbstractHost*> allHost;
        for (auto host : list)
        {
            host->getAllDescendants(allHost);
        }

        for (auto host : allHost)
        {
            QString dataBind = host->getPropertyValue("dataBind").toString();
            if (!dataBind.isEmpty())
            {
                QVariant var(dataBind);
                host->generateEventCode("dataBind", var, m_pPageManager, false);
            }

            QString suffix = "";
            QStringList eventList;
            host->getEventPropertyName(suffix, eventList);
            for (auto str : eventList)
            {
                QString element = host->getPropertyValue(str).toString();
                if (!element.isEmpty())
                {
                    QVariant var(element);
                    host->generateEventCode(str, var, m_pPageManager, false);
                }
            }
        }
    }
}

/*
主要修改说明：

1. 找到控件组的最左上角位置：
   - 遍历所有要粘贴的控件
   - 找到所有控件中最左上角的点（最小的x和y坐标）

2. 计算偏移量：
   - 偏移量 = 鼠标位置 - 控件组最左上角
   - 这样确保控件组的左上角会定位到鼠标位置

3. 应用偏移：
   - 对每个控件应用相同的偏移量
   - 保持控件之间的相对位置关系

4. 效果：
   - 控件组的左上角会精确定位到鼠标点击的位置
   - 多个控件的相对位置保持不变
*/
